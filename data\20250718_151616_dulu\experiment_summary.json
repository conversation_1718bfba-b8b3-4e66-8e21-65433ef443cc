{"participant_id": "dulu", "experiment_start": "2025-07-18T15:17:19.416460", "experiment_end": "2025-07-18T15:31:43.035579", "total_trials": 30, "timing_settings": {"fixation_baseline": 2.0, "question_display": 5.0, "answer_input": null, "curiosity_rating": 10.0, "pupil_baseline": 3.0, "answer_display": 6.0, "pleasure_rating": null, "surprise_rating": null, "gaze_duration_threshold": 1.0}, "trials": [{"trial_num": 1, "question_id": 31, "question": "宫保鸡丁里\"宫保\"是个什么称呼?", "answer": "是\"太子少保\"\"太子太保\"等荣誉性加衔的简称.", "start_time": "2025-07-18T15:17:19.416460", "participant_response": "不知道", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.019641160964966, "question_duration": 5.082382440567017, "input_duration": 3.5359761714935303, "curiosity_rating_duration": 3.1195030212402344, "pupil_baseline_duration": 3.017857313156128, "answer_duration": 6.182041168212891, "pleasure_rating_duration": 2.1520490646362305, "surprise_rating_duration": 1.818549633026123}, "end_time": "2025-07-18T15:17:47.084124"}, {"trial_num": 2, "question_id": 27, "question": "奥斯卡奖的小金人原型是谁?", "answer": "普遍说法称原型是墨西哥演员埃米利奥·费尔南德斯, 但学院官方称是综合了众多优秀男性演员特点. \"奥斯卡\"是学院图书管理员叔叔的名字, 并非原型名字.", "start_time": "2025-07-18T15:17:47.088236", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 3, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.003188133239746, "question_duration": 5.082299709320068, "input_duration": 3.7529194355010986, "curiosity_rating_duration": 3.0649654865264893, "pupil_baseline_duration": 3.0114169120788574, "answer_duration": 6.157740354537964, "pleasure_rating_duration": 3.2228474617004395, "surprise_rating_duration": 1.7512280941009521}, "end_time": "2025-07-18T15:18:15.800372"}, {"trial_num": 3, "question_id": 14, "question": "亚欧大陆以什么为分界线?", "answer": "亚欧大陆分为亚洲和欧洲是因为历史文化差异大, 且俄罗斯地理学家塔季谢夫发现乌拉尔山脉两侧动植物差异明显, 以其为界划分后得到普遍认可.", "start_time": "2025-07-18T15:18:15.803255", "participant_response": "不知道", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.004509687423706, "question_duration": 5.082830429077148, "input_duration": 5.20124077796936, "curiosity_rating_duration": 4.276965618133545, "pupil_baseline_duration": 3.0032074451446533, "answer_duration": 6.11877703666687, "pleasure_rating_duration": 5.028253555297852, "surprise_rating_duration": 4.701013803482056}, "end_time": "2025-07-18T15:18:51.888040"}, {"trial_num": 4, "question_id": 164, "question": "Monster 群共含多少共轭类?", "answer": "Monster群的完整字符表显示其共轭类数量为194.", "start_time": "2025-07-18T15:18:51.890228", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.005838632583618, "question_duration": 5.066946268081665, "input_duration": 2.6744542121887207, "curiosity_rating_duration": 2.8042516708374023, "pupil_baseline_duration": 3.0016427040100098, "answer_duration": 6.0835654735565186, "pleasure_rating_duration": 2.9530789852142334, "surprise_rating_duration": 1.4308650493621826}, "end_time": "2025-07-18T15:19:18.571472"}, {"trial_num": 5, "question_id": 54, "question": "哪个国家拥有世界上唯一非四边形的国旗?", "answer": "尼泊尔 - 尼泊尔拥有世界上唯一的非四边形国旗, 由两个直角三角形组成, 也是唯一纵大于横的国家旗帜, 红色象征勇敢, 蓝色代表和平, 上面有月亮和太阳图案.", "start_time": "2025-07-18T15:19:18.572746", "participant_response": "不知道", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0076241493225098, "question_duration": 5.083827972412109, "input_duration": 6.181195497512817, "curiosity_rating_duration": 3.3579676151275635, "pupil_baseline_duration": 3.008768320083618, "answer_duration": 6.1312971115112305, "pleasure_rating_duration": 2.7594027519226074, "surprise_rating_duration": 1.7813148498535156}, "end_time": "2025-07-18T15:19:49.544554"}, {"trial_num": 6, "question_id": 60, "question": "唯一有声音的蜥蜴是什么?", "answer": "壁虎 - 壁虎是唯一有声音的蜥蜴, 它们拥有真正的声带, 能够发出各种叫声进行交流, 而其他大多数蜥蜴只能发出轻微的嘶嘶声或保持沉默.", "start_time": "2025-07-18T15:19:49.547198", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0174474716186523, "question_duration": 5.06821608543396, "input_duration": 2.8534157276153564, "curiosity_rating_duration": 3.0146796703338623, "pupil_baseline_duration": 3.00736141204834, "answer_duration": 6.121011972427368, "pleasure_rating_duration": 2.0788698196411133, "surprise_rating_duration": 1.6652295589447021}, "end_time": "2025-07-18T15:20:16.056001"}, {"trial_num": 7, "question_id": 20, "question": "纽约\"New York\"为什么不叫\"新约克\", \"约克\"又在哪儿?", "answer": "因为早期翻译习惯等原因未译成\"新约克\", \"约克\"在英国英格兰的北约克郡.", "start_time": "2025-07-18T15:20:16.058262", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0098750591278076, "question_duration": 5.082458972930908, "input_duration": 4.777992486953735, "curiosity_rating_duration": 2.861719846725464, "pupil_baseline_duration": 3.0077648162841797, "answer_duration": 6.088656187057495, "pleasure_rating_duration": 2.493175983428955, "surprise_rating_duration": 2.0014431476593018}, "end_time": "2025-07-18T15:20:45.044656"}, {"trial_num": 8, "question_id": 6, "question": "古代的\"七尺男儿\"有几米高?", "answer": "魏晋南北朝到唐宋时期约为约1. 7米左右.", "start_time": "2025-07-18T15:20:45.047925", "participant_response": "一米九", "curiosity_rating": 4, "pleasure_rating": 3, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0058364868164062, "question_duration": 5.065357208251953, "input_duration": 6.549080848693848, "curiosity_rating_duration": 3.6009581089019775, "pupil_baseline_duration": 3.0046536922454834, "answer_duration": 6.07707142829895, "pleasure_rating_duration": 1.7834384441375732, "surprise_rating_duration": 1.5285687446594238}, "end_time": "2025-07-18T15:21:15.325128"}, {"trial_num": 9, "question_id": 82, "question": "地球上最大的熊是什么?", "answer": "北极熊.", "start_time": "2025-07-18T15:21:15.326632", "participant_response": "黑熊", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.013436794281006, "question_duration": 5.067863464355469, "input_duration": 4.138681650161743, "curiosity_rating_duration": 2.8289475440979004, "pupil_baseline_duration": 3.0030767917633057, "answer_duration": 6.119848966598511, "pleasure_rating_duration": 2.35813307762146, "surprise_rating_duration": 1.8181931972503662}, "end_time": "2025-07-18T15:21:43.382315"}, {"trial_num": 10, "question_id": 162, "question": "计算积分 Integral_{x=0}^{infinity} x^6 / (e^x - 1) dx 的精确值.", "answer": "根据一个积分公式(x的s次方除以e的x次方减1, 从0到无穷大积分), 其结果等于伽马函数Gamma(s+1)乘以zeta函数zeta(s+1).  当s=6时, 结果为6的阶乘乘以zeta(7), 即720乘以zeta(7).", "start_time": "2025-07-18T15:21:43.384098", "participant_response": "？", "curiosity_rating": 2, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0067591667175293, "question_duration": 5.084790229797363, "input_duration": 2.0202548503875732, "curiosity_rating_duration": 2.596156120300293, "pupil_baseline_duration": 3.0124902725219727, "answer_duration": 6.11133599281311, "pleasure_rating_duration": 2.1301469802856445, "surprise_rating_duration": 1.5077054500579834}, "end_time": "2025-07-18T15:22:08.519372"}, {"trial_num": 11, "question_id": 131, "question": "碳的原子序数?", "answer": "6.", "start_time": "2025-07-18T15:22:08.521529", "participant_response": "6", "curiosity_rating": 2, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.004148006439209, "question_duration": 5.083757638931274, "input_duration": 4.430814743041992, "curiosity_rating_duration": 16.061886072158813, "pupil_baseline_duration": 3.0037455558776855, "answer_duration": 6.053231477737427, "pleasure_rating_duration": 2.9959332942962646, "surprise_rating_duration": 2.5691819190979004}, "end_time": "2025-07-18T15:22:51.390979"}, {"trial_num": 12, "question_id": 129, "question": "在巴比伦的世界七大奇迹之一是?", "answer": "空中花园.", "start_time": "2025-07-18T15:22:51.392801", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0022385120391846, "question_duration": 5.066873788833618, "input_duration": 3.2153732776641846, "curiosity_rating_duration": 2.5265378952026367, "pupil_baseline_duration": 3.004833936691284, "answer_duration": 6.073185920715332, "pleasure_rating_duration": 2.054003953933716, "surprise_rating_duration": 2.3910715579986572}, "end_time": "2025-07-18T15:23:18.394686"}, {"trial_num": 13, "question_id": 17, "question": "谁是历史上\"第一个吃螃蟹的人\"?", "answer": "相传大禹治水时, 壮士巴解是第一个吃螃蟹的人.", "start_time": "2025-07-18T15:23:18.397287", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0080158710479736, "question_duration": 5.060137987136841, "input_duration": 1.8438503742218018, "curiosity_rating_duration": 2.434305429458618, "pupil_baseline_duration": 3.0045688152313232, "answer_duration": 6.085963010787964, "pleasure_rating_duration": 2.0289671421051025, "surprise_rating_duration": 1.9658746719360352}, "end_time": "2025-07-18T15:23:43.489547"}, {"trial_num": 14, "question_id": 158, "question": "写出 K3 曲面二阶上同调交叉形式的符号 (signature).", "answer": "K3曲面第二个上同调群的交叉形式可以分解为3U与2个负E8的直和, 因此其符号差为(3, 19).", "start_time": "2025-07-18T15:23:43.491357", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0207793712615967, "question_duration": 5.073908567428589, "input_duration": 2.658270835876465, "curiosity_rating_duration": 2.551050901412964, "pupil_baseline_duration": 3.011232614517212, "answer_duration": 6.107899904251099, "pleasure_rating_duration": 2.3891756534576416, "surprise_rating_duration": 1.6737234592437744}, "end_time": "2025-07-18T15:24:09.639188"}, {"trial_num": 15, "question_id": 12, "question": "3A游戏指的是哪3A?", "answer": "3A游戏中的3A指大量的金钱(A lot of money), 大量的资源(A lot of resources), 大量的时间(A lot of time).", "start_time": "2025-07-18T15:24:09.640924", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0142982006073, "question_duration": 5.0649261474609375, "input_duration": 2.156188726425171, "curiosity_rating_duration": 2.245086908340454, "pupil_baseline_duration": 3.011117935180664, "answer_duration": 6.09531044960022, "pleasure_rating_duration": 2.9116575717926025, "surprise_rating_duration": 2.1615238189697266}, "end_time": "2025-07-18T15:24:35.966045"}, {"trial_num": 16, "question_id": 58, "question": "什么品种的狗是唯一可以在美国某些法庭上作为证据的动物?", "answer": "寻血猎犬 - 寻血猎犬是世界上第一类以嗅觉为证据被法庭所采纳的犬种, 在美国某些法庭上可以作为证据, 它们拥有超强的嗅觉能力, 能够追踪数天前的气味痕迹.", "start_time": "2025-07-18T15:24:35.968281", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0030970573425293, "question_duration": 5.082693815231323, "input_duration": 4.8460986614227295, "curiosity_rating_duration": 2.490452766418457, "pupil_baseline_duration": 3.0109007358551025, "answer_duration": 6.1252968311309814, "pleasure_rating_duration": 1.9362494945526123, "surprise_rating_duration": 1.6618101596832275}, "end_time": "2025-07-18T15:25:03.789215"}, {"trial_num": 17, "question_id": 68, "question": "埃及最大的神庙是哪个?", "answer": "卡纳克 - 卡纳克神庙是埃及最大的神庙, 位于卢克索北部, 始建于3900多年前, 是地球上最大的用柱子支撑的建筑群, 神庙内有大小20余座神殿, 134根巨型石柱.", "start_time": "2025-07-18T15:25:03.793596", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0194509029388428, "question_duration": 5.063403367996216, "input_duration": 2.706387519836426, "curiosity_rating_duration": 2.060088634490967, "pupil_baseline_duration": 3.0139503479003906, "answer_duration": 6.122048377990723, "pleasure_rating_duration": 5.6799046993255615, "surprise_rating_duration": 1.7820231914520264}, "end_time": "2025-07-18T15:25:32.903432"}, {"trial_num": 18, "question_id": 76, "question": "哪个澳大利亚州以前被称为范迪门斯地?", "answer": "塔斯马尼亚州.", "start_time": "2025-07-18T15:25:32.904947", "participant_response": "？", "curiosity_rating": 3, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.014751672744751, "question_duration": 5.075216054916382, "input_duration": 2.43721604347229, "curiosity_rating_duration": 2.2124006748199463, "pupil_baseline_duration": 3.0140724182128906, "answer_duration": 6.059998273849487, "pleasure_rating_duration": 2.5692737102508545, "surprise_rating_duration": 2.9950098991394043}, "end_time": "2025-07-18T15:25:59.946782"}, {"trial_num": 19, "question_id": 85, "question": "贝多芬唯一的歌剧叫什么名字?", "answer": "《费德里奥》.", "start_time": "2025-07-18T15:25:59.948776", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.016458511352539, "question_duration": 5.0626139640808105, "input_duration": 5.926342725753784, "curiosity_rating_duration": 2.037752866744995, "pupil_baseline_duration": 3.0173583030700684, "answer_duration": 6.067444086074829, "pleasure_rating_duration": 1.9945764541625977, "surprise_rating_duration": 4.003326177597046}, "end_time": "2025-07-18T15:26:30.733760"}, {"trial_num": 20, "question_id": 104, "question": "第一位独自飞越大西洋的人的姓氏是什么?", "answer": "林德伯格.", "start_time": "2025-07-18T15:26:30.736353", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 3, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.0123746395111084, "question_duration": 5.069164752960205, "input_duration": 2.6834559440612793, "curiosity_rating_duration": 2.101144790649414, "pupil_baseline_duration": 3.0082385540008545, "answer_duration": 6.0609495639801025, "pleasure_rating_duration": 1.8147408962249756, "surprise_rating_duration": 3.156219959259033}, "end_time": "2025-07-18T15:26:57.310550"}, {"trial_num": 21, "question_id": 112, "question": "写下《审判》的欧洲作家的姓氏是什么?", "answer": "卡夫卡.", "start_time": "2025-07-18T15:26:57.312620", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 2, "timing_data": {"baseline_duration": 2.004305362701416, "question_duration": 5.067534923553467, "input_duration": 3.270191192626953, "curiosity_rating_duration": 2.446591854095459, "pupil_baseline_duration": 3.0109777450561523, "answer_duration": 6.0574774742126465, "pleasure_rating_duration": 2.156573534011841, "surprise_rating_duration": 2.8336358070373535}, "end_time": "2025-07-18T15:27:24.823028"}, {"trial_num": 22, "question_id": 66, "question": "西班牙的国花是什么?", "answer": "康乃馨 - 康乃馨是西班牙的国花, 原产于欧洲南部地中海沿岸, 已有2000多年栽培历史, 是一种繁茂且生命力顽强的花卉, 能够适应地中海不同地区和气候.", "start_time": "2025-07-18T15:27:24.824764", "participant_response": "?", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0111827850341797, "question_duration": 5.068010330200195, "input_duration": 2.816091775894165, "curiosity_rating_duration": 2.243685007095337, "pupil_baseline_duration": 3.003007173538208, "answer_duration": 6.139191389083862, "pleasure_rating_duration": 2.0437960624694824, "surprise_rating_duration": 2.3338701725006104}, "end_time": "2025-07-18T15:27:51.149724"}, {"trial_num": 23, "question_id": 98, "question": "\"空中花园\"位于哪个古城?", "answer": "巴比伦.", "start_time": "2025-07-18T15:27:51.151655", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 3, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.003312349319458, "question_duration": 5.092001438140869, "input_duration": 19.963144779205322, "curiosity_rating_duration": 2.9434945583343506, "pupil_baseline_duration": 3.0095160007476807, "answer_duration": 6.052741050720215, "pleasure_rating_duration": 2.0469679832458496, "surprise_rating_duration": 1.1955385208129883}, "end_time": "2025-07-18T15:28:34.125821"}, {"trial_num": 24, "question_id": 34, "question": "\"倒立喝水\", 能喝进肚子里吗?", "answer": "能, 水是通过食管的肌肉收缩蠕动进到胃里, 受重力影响不明显.", "start_time": "2025-07-18T15:28:34.127519", "participant_response": "能", "curiosity_rating": 3, "pleasure_rating": 2, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0175726413726807, "question_duration": 5.0641937255859375, "input_duration": 3.1498894691467285, "curiosity_rating_duration": 2.573740243911743, "pupil_baseline_duration": 3.0165138244628906, "answer_duration": 6.089118480682373, "pleasure_rating_duration": 2.2697315216064453, "surprise_rating_duration": 1.8509032726287842}, "end_time": "2025-07-18T15:29:00.822976"}, {"trial_num": 25, "question_id": 72, "question": "据说曾担任亚历山大大帝导师的著名希腊哲学家是谁?", "answer": "亚里士多德.", "start_time": "2025-07-18T15:29:00.824384", "participant_response": "？", "curiosity_rating": 4, "pleasure_rating": 4, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0044612884521484, "question_duration": 5.101169586181641, "input_duration": 4.338600158691406, "curiosity_rating_duration": 2.066737651824951, "pupil_baseline_duration": 3.0061187744140625, "answer_duration": 6.060766696929932, "pleasure_rating_duration": 2.097463607788086, "surprise_rating_duration": 3.1251769065856934}, "end_time": "2025-07-18T15:29:29.285079"}, {"trial_num": 26, "question_id": 55, "question": "世界上人口密度最高的国家是哪个?", "answer": "摩纳哥 - 摩纳哥是世界上人口密度最高的国家, 每平方公里约19497人, 国土面积仅2. 08平方公里, 是世界第二小的国家, 也是全球超级富豪的聚集地和避税天堂.", "start_time": "2025-07-18T15:29:29.287183", "participant_response": "印度", "curiosity_rating": 2, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.009949207305908, "question_duration": 5.068040370941162, "input_duration": 5.570457696914673, "curiosity_rating_duration": 1.8614294528961182, "pupil_baseline_duration": 3.0201964378356934, "answer_duration": 6.123474359512329, "pleasure_rating_duration": 2.4055933952331543, "surprise_rating_duration": 1.6449272632598877}, "end_time": "2025-07-18T15:29:57.651408"}, {"trial_num": 27, "question_id": 149, "question": "中国的首都是哪里?", "answer": "北京", "start_time": "2025-07-18T15:29:57.655023", "participant_response": "北京", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.0153310298919678, "question_duration": 5.064463138580322, "input_duration": 3.134828567504883, "curiosity_rating_duration": 1.9995944499969482, "pupil_baseline_duration": 3.0120668411254883, "answer_duration": 6.0496745109558105, "pleasure_rating_duration": 2.678107976913452, "surprise_rating_duration": 1.5285396575927734}, "end_time": "2025-07-18T15:30:23.794963"}, {"trial_num": 28, "question_id": 143, "question": "\"热\"的反义词是什么?", "answer": "冷", "start_time": "2025-07-18T15:30:23.799751", "participant_response": "冷", "curiosity_rating": 1, "pleasure_rating": 1, "surprise_rating": 1, "timing_data": {"baseline_duration": 2.004406452178955, "question_duration": 5.081404447555542, "input_duration": 4.504195213317871, "curiosity_rating_duration": 1.6965069770812988, "pupil_baseline_duration": 3.013718605041504, "answer_duration": 6.064864635467529, "pleasure_rating_duration": 1.638580322265625, "surprise_rating_duration": 1.4606266021728516}, "end_time": "2025-07-18T15:30:49.920536"}, {"trial_num": 29, "question_id": 16, "question": "为什么冰过的西瓜吃起来感觉要甜一些?", "answer": "低温时α果糖向β果糖方向转化, β型果糖甜度更高.", "start_time": "2025-07-18T15:30:49.922414", "participant_response": "？", "curiosity_rating": 5, "pleasure_rating": 5, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0144896507263184, "question_duration": 5.083390712738037, "input_duration": 2.172147274017334, "curiosity_rating_duration": 3.70759654045105, "pupil_baseline_duration": 3.0180773735046387, "answer_duration": 6.086175441741943, "pleasure_rating_duration": 2.2558140754699707, "surprise_rating_duration": 2.005674362182617}, "end_time": "2025-07-18T15:31:16.927547"}, {"trial_num": 30, "question_id": 25, "question": "\"傻瓜\"最早指的是哪种瓜?", "answer": "不是指真正的瓜.  古代瓜州的瓜子族诚实苦干, 被误认为愚蠢呆傻, \"傻瓜\"由\"瓜子\"演变而来.", "start_time": "2025-07-18T15:31:16.930114", "participant_response": "？", "curiosity_rating": 5, "pleasure_rating": 4, "surprise_rating": 3, "timing_data": {"baseline_duration": 2.0092334747314453, "question_duration": 5.0649402141571045, "input_duration": 2.9369616508483887, "curiosity_rating_duration": 2.2371904850006104, "pupil_baseline_duration": 3.0097453594207764, "answer_duration": 6.1226677894592285, "pleasure_rating_duration": 2.3558709621429443, "surprise_rating_duration": 1.6946043968200684}, "end_time": "2025-07-18T15:31:43.035579"}]}