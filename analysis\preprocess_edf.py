#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EDF瞳孔数据预处理模块
功能：
1. 使用pyedfread读取EDF文件
2. 5点滑动窗口均值平滑
3. 眨眼检测和线性插值处理
4. 基线校正
5. 输出预处理后的DataFrame
"""

import os
import pandas as pd
import numpy as np
import logging
import json
import pickle
from typing import Dict, List, Tuple, Optional, Union
from scipy import interpolate
from scipy.ndimage import uniform_filter1d
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('数据预处理')

# 设置中文字体
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试多种中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']
        available_fonts = [f.name for f in fm.fontManager.ttflist]

        for font in chinese_fonts:
            if font in available_fonts:
                plt.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                logger.info(f"使用中文字体: {font}")
                return font

        # 如果没有找到中文字体，使用默认字体
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        logger.warning("未找到中文字体，使用默认字体")
        return 'DejaVu Sans'
    except Exception as e:
        logger.error(f"字体设置失败: {e}")
        return 'DejaVu Sans'

# 初始化字体
current_font = setup_chinese_font()

class EyeDataPreprocessor:
    """EDF瞳孔数据预处理器"""
    
    def __init__(self,
                smooth_window=5,
                blink_interpolation_window=40,  # 启用眨眼插值以便看到处理效果
                interpolation_method='cubic',  #
                min_pupil_size=100.0,
                max_pupil_size=6000.0,
                sampling_rate=1000,
                velocity_threshold=500.0,
                quadratic_fit_points=100,  # 使用前后10个点进行拟合
                min_valid_segment_length=50,  # 最小有效区间长度（0代表关闭）
                eye_selection='binocular',  # 眼睛选择：'left', 'right', 'binocular'
                enable_pfe_correction=False,  # 是否启用PFE矫正
                pfe_angle_threshold_degrees=60.0):  # PFE角度阈值（度）
        

        """
        初始化预处理器

        Args:
            smooth_window: 滑动窗口大小（数据点数）
            blink_interpolation_window: 眨眼前后插值窗口大小（毫秒）
            interpolation_method: 插值方法 ('linear', 'cubic', 'nearest', 'quadratic')
            min_pupil_size: 最小有效瞳孔直径
            max_pupil_size: 最大有效瞳孔直径
            sampling_rate: 采样率（Hz）
            velocity_threshold: 速度阈值（像素/毫秒），超过此速度的点将被过滤
            quadratic_fit_points: 二次曲线拟合时使用的前后数据点数（默认10个）
            min_valid_segment_length: 最小有效区间长度（数据点数，0代表关闭此功能）
            eye_selection: 眼睛选择 ('left'=仅左眼, 'right'=仅右眼, 'binocular'=双眼平均)
            enable_pfe_correction: 是否启用PFE瞳孔前缩误差矫正
            pfe_angle_threshold_degrees: PFE角度阈值（度），只有在此角度内才进行PFE补偿
        """
        self.smooth_window = smooth_window
        self.blink_interpolation_window_before = 120
        self.blink_interpolation_window_after = 200
        self.interpolation_method = interpolation_method
        self.min_pupil_size = min_pupil_size
        self.max_pupil_size = max_pupil_size
        self.sampling_rate = sampling_rate
        self.velocity_threshold = velocity_threshold
        self.quadratic_fit_points = quadratic_fit_points
        self.min_valid_segment_length = min_valid_segment_length
        self.eye_selection = eye_selection

        # PFE矫正参数
        self.enable_pfe_correction = enable_pfe_correction
        self.pfe_angle_threshold_degrees = pfe_angle_threshold_degrees

        # 计算插值窗口对应的数据点数
        self.blink_window_samples = int((self.blink_interpolation_window_before+self.blink_interpolation_window_after) * sampling_rate / 1000)

        # 验证眼睛选择参数
        valid_eye_selections = ['left', 'right', 'binocular']
        if eye_selection not in valid_eye_selections:
            logger.warning(f"无效的眼睛选择参数 '{eye_selection}'，使用默认值 'binocular'")
            self.eye_selection = 'binocular'

        logger.info(f"数据预处理器初始化完成")
        logger.info(f"  - 滑动窗口: {smooth_window} 个数据点")
        logger.info(f"  - 眨眼插值窗口: {blink_interpolation_window} ms ({self.blink_window_samples} 个数据点)")
        logger.info(f"  - 插值方法: {interpolation_method}")
        logger.info(f"  - 瞳孔直径范围: {min_pupil_size} - {max_pupil_size}")
        logger.info(f"  - 速度阈值: {velocity_threshold} 像素/毫秒")
        logger.info(f"  - 二次曲线拟合点数: {quadratic_fit_points} 个数据点")
        logger.info(f"  - 最小有效区间长度: {min_valid_segment_length} 个数据点 ({'关闭' if min_valid_segment_length == 0 else '启用'})")
        logger.info(f"  - 眼睛选择: {self.eye_selection}")
        logger.info(f"  - PFE矫正: {'启用' if self.enable_pfe_correction else '关闭'}")
        logger.info(f"  - PFE角度阈值: {self.pfe_angle_threshold_degrees}度")

    def get_processing_params(self) -> dict:
        """获取当前的处理参数"""
        return {
            'smooth_window': self.smooth_window,
            'blink_interpolation_window_before': self.blink_interpolation_window_before,
            'blink_interpolation_window_after': self.blink_interpolation_window_after,
            'interpolation_method': self.interpolation_method,
            'min_pupil_size': self.min_pupil_size,
            'max_pupil_size': self.max_pupil_size,
            'sampling_rate': self.sampling_rate,
            'velocity_threshold': self.velocity_threshold,
            'quadratic_fit_points': self.quadratic_fit_points,
            'min_valid_segment_length': self.min_valid_segment_length,
            'eye_selection': self.eye_selection,
            'enable_pfe_correction': self.enable_pfe_correction,
            'pfe_angle_threshold_degrees': self.pfe_angle_threshold_degrees
        }

    def get_cache_paths(self, edf_path: str) -> Tuple[str, str]:
        """获取缓存文件路径"""
        # 获取EDF文件的目录和文件名
        edf_dir = os.path.dirname(edf_path)
        edf_name = os.path.splitext(os.path.basename(edf_path))[0]

        # 创建data_cache子目录
        cache_dir = os.path.join(edf_dir, "data_cache")
        os.makedirs(cache_dir, exist_ok=True)

        # 生成缓存文件路径
        data_path = os.path.join(cache_dir, f"{edf_name}_processed.pkl")
        params_path = os.path.join(cache_dir, f"{edf_name}_params.json")
        return data_path, params_path

    def get_raw_cache_paths(self, edf_path: str) -> Tuple[str, str, str]:
        """获取原始EDF数据缓存文件路径"""
        # 获取EDF文件的目录和文件名
        edf_dir = os.path.dirname(edf_path)
        edf_name = os.path.splitext(os.path.basename(edf_path))[0]

        # 创建data_cache子目录
        cache_dir = os.path.join(edf_dir, "data_cache")
        os.makedirs(cache_dir, exist_ok=True)

        # 生成缓存文件路径
        samples_path = os.path.join(cache_dir, f"{edf_name}_samples.pkl")
        events_path = os.path.join(cache_dir, f"{edf_name}_events.pkl")
        messages_path = os.path.join(cache_dir, f"{edf_name}_messages.pkl")
        return samples_path, events_path, messages_path

    def load_cached_data(self, edf_path: str) -> Optional[pd.DataFrame]:
        """尝试加载缓存的数据"""
        return None
        data_path, params_path = self.get_cache_paths(edf_path)

        if not (os.path.exists(data_path) and os.path.exists(params_path)):
            return None

        try:
            # 加载保存的参数
            with open(params_path, 'r', encoding='utf-8') as f:
                saved_params = json.load(f)

            # 比较参数
            current_params = self.get_processing_params()
            if saved_params == current_params:
                # 参数相同，加载数据
                with open(data_path, 'rb') as f:
                    cached_data = pickle.load(f)
                logger.info(f"使用缓存数据: {data_path}")
                return cached_data
            else:
                logger.info("处理参数已变更，重新处理数据")
                return None
        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            return None

    def save_cached_data(self, edf_path: str, data: pd.DataFrame):
        """保存处理后的数据和参数"""
        data_path, params_path = self.get_cache_paths(edf_path)

        try:
            # 保存数据
            with open(data_path, 'wb') as f:
                pickle.dump(data, f)

            # 保存参数
            params = self.get_processing_params()
            with open(params_path, 'w', encoding='utf-8') as f:
                json.dump(params, f, indent=2, ensure_ascii=False)

            logger.info(f"缓存数据已保存: {data_path}")
        except Exception as e:
            logger.error(f"保存缓存失败: {e}")

    def load_cached_raw_data(self, edf_path: str) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """尝试加载缓存的原始EDF数据"""
        samples_path, events_path, messages_path = self.get_raw_cache_paths(edf_path)

        if not (os.path.exists(samples_path) and os.path.exists(events_path) and os.path.exists(messages_path)):
            return None, None, None

        try:
            # 加载三个DataFrame
            with open(samples_path, 'rb') as f:
                samples = pickle.load(f)
            with open(events_path, 'rb') as f:
                events = pickle.load(f)
            with open(messages_path, 'rb') as f:
                messages = pickle.load(f)

            logger.info(f"使用缓存的原始EDF数据: {samples_path}")
            return samples, events, messages
        except Exception as e:
            logger.warning(f"加载原始EDF缓存失败: {e}")
            return None, None, None

    def save_cached_raw_data(self, edf_path: str, samples: pd.DataFrame, events: pd.DataFrame, messages: pd.DataFrame):
        """保存原始EDF数据到缓存"""
        samples_path, events_path, messages_path = self.get_raw_cache_paths(edf_path)

        try:
            # 保存三个DataFrame
            with open(samples_path, 'wb') as f:
                pickle.dump(samples, f)
            with open(events_path, 'wb') as f:
                pickle.dump(events, f)
            with open(messages_path, 'wb') as f:
                pickle.dump(messages, f)

            logger.info(f"原始EDF数据缓存已保存: {samples_path}")
        except Exception as e:
            logger.error(f"保存原始EDF缓存失败: {e}")

    def check_pyedfread(self) -> bool:
        """检查pyedfread是否可用"""
        try:
            import pyedfread
            return True
        except ImportError:
            logger.error("❌ pyedfread 未安装")
            logger.error("请安装: pip install git+https://github.com/s-ccs/pyedfread")
            return False
    
    def load_edf_data(self, edf_path: str) -> Tuple[Optional[pd.DataFrame], Optional[pd.DataFrame], Optional[pd.DataFrame]]:
        """
        使用pyedfread加载EDF文件

        Args:
            edf_path: EDF文件路径

        Returns:
            Tuple[samples, events, messages]: 样本数据、事件数据、消息数据
        """
        # 首先尝试加载缓存的原始数据
        cached_samples, cached_events, cached_messages = self.load_cached_raw_data(edf_path)
        if cached_samples is not None:
            logger.info("使用缓存的原始EDF数据")
            return cached_samples, cached_events, cached_messages

        # 缓存不存在，从EDF文件读取
        if not self.check_pyedfread():
            return None, None, None

        try:
            import pyedfread
            logger.info(f"正在读取EDF文件: {edf_path}")

            # 读取EDF文件
            samples, events, messages = pyedfread.read_edf(edf_path)

            logger.info(f"成功读取EDF文件")
            logger.info(f"  - 样本数: {len(samples)}")
            logger.info(f"  - 事件数: {len(events)}")
            logger.info(f"  - 消息数: {len(messages)}")

            # 保存到缓存
            self.save_cached_raw_data(edf_path, samples, events, messages)

            return samples, events, messages

        except Exception as e:
            logger.error(f"❌ 读取EDF文件失败: {e}")
            return None, None, None
    
    def extract_blink_periods(self, events: pd.DataFrame) -> List[Tuple[int, int, int]]:
        """
        从事件数据中提取眨眼时间段
        
        Args:
            events: 事件数据DataFrame
            
        Returns:
            List[Tuple[start_time, end_time, eye]]: 眨眼时间段列表
        """
        blink_periods = []
        
        # 筛选眨眼事件
        blink_events = events[events['type'] == 'blink'].copy()
        
        if len(blink_events) == 0:
            logger.warning("未找到眨眼事件")
            return blink_periods
        
        for _, blink in blink_events.iterrows():
            start_time = blink['start']
            end_time = blink['end']
            eye = blink['eye']  # 0=左眼, 1=右眼
            
            # 扩展眨眼时间段，包括前后的插值窗口
            extended_start = start_time - self.blink_interpolation_window_before
            extended_end = end_time + self.blink_interpolation_window_after
            
            blink_periods.append((extended_start, extended_end, eye))
        
        logger.info(f"提取到 {len(blink_periods)} 个眨眼时间段")
        return blink_periods
    
    def clean_pupil_data(self, samples: pd.DataFrame) -> pd.DataFrame:
        """
        清理瞳孔数据，过滤异常值
        
        Args:
            samples: 原始样本数据
            
        Returns:
            pd.DataFrame: 清理后的数据
        """
        cleaned_samples = samples.copy()
        
        # 过滤左眼瞳孔数据
        if 'pa_left' in cleaned_samples.columns:
            left_before = (~cleaned_samples['pa_left'].isna()).sum()
            mask = (cleaned_samples['pa_left'] < self.min_pupil_size) | (cleaned_samples['pa_left'] > self.max_pupil_size)
            cleaned_samples.loc[mask, 'pa_left'] = np.nan
            left_after = (~cleaned_samples['pa_left'].isna()).sum()
            logger.info(f"左眼数据清理: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个异常值)")
        
        # 过滤右眼瞳孔数据
        if 'pa_right' in cleaned_samples.columns:
            right_before = (~cleaned_samples['pa_right'].isna()).sum()
            mask = (cleaned_samples['pa_right'] < self.min_pupil_size) | (cleaned_samples['pa_right'] > self.max_pupil_size)
            cleaned_samples.loc[mask, 'pa_right'] = np.nan
            right_after = (~cleaned_samples['pa_right'].isna()).sum()
            logger.info(f"右眼数据清理: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个异常值)")
        
        return cleaned_samples

    def apply_velocity_filter(self, samples: pd.DataFrame) -> pd.DataFrame:
        """
        应用速度阈值过滤，去除变化过快的数据点

        Args:
            samples: 样本数据

        Returns:
            pd.DataFrame: 速度过滤后的数据
        """
        filtered_samples = samples.copy()

        # 处理左眼瞳孔数据
        if 'pa_left' in filtered_samples.columns:
            left_before = (~filtered_samples['pa_left'].isna()).sum()
            filtered_samples['pa_left'] = self._filter_by_velocity(
                filtered_samples['time'],
                filtered_samples['pa_left']
            )
            left_after = (~filtered_samples['pa_left'].isna()).sum()
            logger.info(f"左眼速度过滤: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个高速变化点)")

        # 处理右眼瞳孔数据
        if 'pa_right' in filtered_samples.columns:
            right_before = (~filtered_samples['pa_right'].isna()).sum()
            filtered_samples['pa_right'] = self._filter_by_velocity(
                filtered_samples['time'],
                filtered_samples['pa_right']
            )
            right_after = (~filtered_samples['pa_right'].isna()).sum()
            logger.info(f"右眼速度过滤: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个高速变化点)")

        return filtered_samples

    def _filter_by_velocity(self, time_series: pd.Series, pupil_series: pd.Series) -> pd.Series:
        """
        根据速度阈值过滤瞳孔数据

        Args:
            time_series: 时间序列
            pupil_series: 瞳孔直径序列

        Returns:
            pd.Series: 过滤后的瞳孔数据
        """
        filtered_pupil = pupil_series.copy()

        # 计算相邻点之间的速度（像素/毫秒）
        time_diff = time_series.diff()  # 时间差（毫秒）
        pupil_diff = pupil_series.diff()  # 瞳孔直径差（像素）

        # 避免除零错误
        velocity = np.where(time_diff > 0, np.abs(pupil_diff) / time_diff, 0)

        # 标记超过速度阈值的点
        high_velocity_mask = velocity > self.velocity_threshold

        # 将高速变化的点设为NaN
        filtered_pupil[high_velocity_mask] = np.nan

        return filtered_pupil

    def apply_valid_segment_filter(self, samples: pd.DataFrame) -> pd.DataFrame:
        """
        应用有效区间长度过滤，将短于阈值的有效数据段设为NaN

        Args:
            samples: 样本数据

        Returns:
            pd.DataFrame: 有效区间过滤后的数据
        """
        if self.min_valid_segment_length == 0:
            logger.info("有效区间长度过滤已关闭")
            return samples

        filtered_samples = samples.copy()

        # 处理左眼瞳孔数据
        if 'pa_left' in filtered_samples.columns:
            left_before = (~filtered_samples['pa_left'].isna()).sum()
            filtered_samples['pa_left'] = self._filter_short_segments(
                filtered_samples['pa_left']
            )
            left_after = (~filtered_samples['pa_left'].isna()).sum()
            logger.info(f"左眼有效区间过滤: {left_before} -> {left_after} (过滤了 {left_before - left_after} 个短区间数据点)")

        # 处理右眼瞳孔数据
        if 'pa_right' in filtered_samples.columns:
            right_before = (~filtered_samples['pa_right'].isna()).sum()
            filtered_samples['pa_right'] = self._filter_short_segments(
                filtered_samples['pa_right']
            )
            right_after = (~filtered_samples['pa_right'].isna()).sum()
            logger.info(f"右眼有效区间过滤: {right_before} -> {right_after} (过滤了 {right_before - right_after} 个短区间数据点)")

        return filtered_samples

    def _filter_short_segments(self, pupil_series: pd.Series) -> pd.Series:
        """
        过滤短于阈值的有效数据段

        Args:
            pupil_series: 瞳孔直径序列

        Returns:
            pd.Series: 过滤后的瞳孔数据
        """
        filtered_pupil = pupil_series.copy()

        # 找到所有NaN的位置
        nan_mask = pupil_series.isna()
        valid_mask = ~nan_mask

        # 使用numpy来找到连续的有效数据段
        valid_array = valid_mask.values.astype(int)

        # 找到有效数据段的开始和结束位置
        diff_array = np.diff(np.concatenate(([0], valid_array, [0])))

        # 找到段开始位置（从0变为1）
        start_positions = np.where(diff_array == 1)[0]

        # 找到段结束位置（从1变为0）
        end_positions = np.where(diff_array == -1)[0] - 1

        # 检查每个有效数据段的长度
        segments_to_remove = []
        for start_idx, end_idx in zip(start_positions, end_positions):
            segment_length = end_idx - start_idx + 1
            if segment_length < self.min_valid_segment_length:
                segments_to_remove.append((start_idx, end_idx))
                logger.debug(f"标记短区间: 索引 {start_idx}-{end_idx}, 长度 {segment_length}")
            else:
                logger.debug(f"保留长区间: 索引 {start_idx}-{end_idx}, 长度 {segment_length}")

        # 将短区间设为NaN
        for start_idx, end_idx in segments_to_remove:
            filtered_pupil.iloc[start_idx:end_idx+1] = np.nan

        if segments_to_remove:
            logger.info(f"过滤了 {len(segments_to_remove)} 个短于 {self.min_valid_segment_length} 个数据点的有效区间")
        else:
            logger.info("没有找到需要过滤的短区间")

        return filtered_pupil

    def _quadratic_interpolation(self,
                                time_points: np.ndarray,
                                fit_times: np.ndarray,
                                fit_values: np.ndarray,
                                poly_deg: int) -> np.ndarray:
        """
        使用 Akima 分段三次插值进行拟合；若结果超出瞳孔尺寸范围则回退到线性插值。

        Args:
            time_points: 需要插值的时间点
            fit_times: 拟合使用的时间点
            fit_values: 拟合使用的瞳孔值
            poly_deg: 保留以兼容原有接口（Akima 不使用该参数）
        Returns:
            np.ndarray: 插值后的瞳孔值
        """
        try:
            #PCHIP
            # 转为 numpy 并保证时间单调递增
            fit_times_np = np.asarray(fit_times)
            fit_values_np = np.asarray(fit_values)
            order = np.argsort(fit_times_np)

            # PCHIP 插值器（scipy.interpolate 已在文件头部导入）
            pchip = interpolate.PchipInterpolator(fit_times_np[order], fit_values_np[order], extrapolate=False)
            interpolated_values = pchip(time_points)
            #
            
            # Akima
            # fit_times_np = np.asarray(fit_times)
            # fit_values_np = np.asarray(fit_values)
            # order = np.argsort(fit_times_np)

            # akima = interpolate.Akima1DInterpolator(
            #     fit_times_np[order], fit_values_np[order]
            # )
            # interpolated_values = akima(time_points)
            #

            # 越界检查
            out_of_range_mask = (
                (interpolated_values < self.min_pupil_size) |
                (interpolated_values > self.max_pupil_size)
            )
            if np.any(out_of_range_mask):
                logger.warning(
                    f"Akima 插值结果中有 {np.sum(out_of_range_mask)} 个点超出范围 "
                    f"({self.min_pupil_size}-{self.max_pupil_size})，改用线性插值"
                )
                return np.interp(time_points, fit_times_np, fit_values_np)

            logger.debug("Akima 插值成功，所有插值点均在有效范围内")
            return np.asarray(interpolated_values)

        except Exception as e:
            logger.warning(f"Akima 插值失败，回退到线性插值: {e}")
            return np.interp(time_points, fit_times, fit_values)

    # def _quadratic_interpolation(self, time_points: np.ndarray,
    #                             fit_times: np.ndarray,
    #                             fit_values: np.ndarray,
    #                             poly_deg: int) -> np.ndarray:
    #     """
    #     使用二次曲线拟合进行插值，如果插值结果超出瞳孔尺寸范围则改用线性插值

    #     Args:
    #         time_points: 需要插值的时间点
    #         fit_times: 用于拟合的时间点
    #         fit_values: 用于拟合的瞳孔值

    #     Returns:
    #         np.ndarray: 插值后的瞳孔值
    #     """
    #     try:
    #         # 使用numpy的polyfit进行二次多项式拟合
    #         # 拟合二次函数 y = ax^2 + bx + c

    #         coefficients = np.polyfit(fit_times, fit_values, deg=poly_deg)
                

    #         # 使用拟合的系数计算插值
    #         interpolated_values = np.polyval(coefficients, time_points)

    #         # 检查插值结果是否超出瞳孔尺寸范围
    #         out_of_range_mask = (interpolated_values < self.min_pupil_size) | (interpolated_values > self.max_pupil_size)

    #         if np.any(out_of_range_mask):
    #             logger.warning(f"二次曲线拟合结果中有{np.sum(out_of_range_mask)}个点超出瞳孔尺寸范围({self.min_pupil_size}-{self.max_pupil_size})，改用线性插值")
    #             # 如果有超出范围的点，改用线性插值
    #             return np.interp(time_points, fit_times, fit_values)

    #         logger.debug(f"二次曲线拟合成功，所有插值点都在有效范围内")
    #         return interpolated_values

    #     except Exception as e:
    #         logger.warning(f"二次曲线拟合失败，回退到线性插值: {e}")
    #         # 如果二次拟合失败，回退到线性插值
    #         return np.interp(time_points, fit_times, fit_values)

    def apply_blink_interpolation(self, samples: pd.DataFrame, blink_periods: List[Tuple[int, int, int]]) -> pd.DataFrame:
        """
        对眨眼时间段进行插值（支持线性、三次样条和二次曲线插值）

        Args:
            samples: 样本数据
            blink_periods: 眨眼时间段列表

        Returns:
            pd.DataFrame: 插值后的数据
        """
        interpolated_samples = samples.copy()
        
        for start_time, end_time, eye in blink_periods:
            # 找到对应的数据段
            mask = (samples['time'] >= start_time) & (samples['time'] <= end_time)
            indices = samples.index[mask]
            
            if len(indices) == 0:
                continue
            
            # 确定要处理的瞳孔数据列
            pupil_col = 'pa_left' if eye == 0 else 'pa_right'
            
            if pupil_col not in samples.columns:
                continue
            
            # 获取需要插值的时间点
            if len(indices) == 0:
                continue

            time_points = samples.loc[indices, 'time'].values

            # 根据插值方法选择不同的策略
            if self.interpolation_method == 'quadratic':
                # 二次曲线拟合插值
                interpolated_values = self._apply_quadratic_interpolation(
                    samples, indices, pupil_col, time_points, 2
                )
            elif self.interpolation_method == "cubic":
                # 三次曲线拟合插值
                interpolated_values = self._apply_quadratic_interpolation(
                    samples, indices, pupil_col, time_points, 3
                )
            else:
                # 传统的线性插值或其他方法
                interpolated_values = self._apply_traditional_interpolation(
                    samples, indices, pupil_col, time_points
                )

            # 应用插值结果
            if interpolated_values is not None:
                interpolated_samples.loc[indices, pupil_col] = interpolated_values
        
        logger.info(f"完成眨眼插值处理，处理了 {len(blink_periods)} 个眨眼时间段")
        return interpolated_samples

    def _apply_quadratic_interpolation(self, samples: pd.DataFrame,
                                     indices: pd.Index,
                                     pupil_col: str,
                                     time_points: np.ndarray,
                                     poly_deg: int) -> Optional[np.ndarray]:
        """
        应用二次曲线拟合插值

        Args:
            samples: 样本数据
            indices: 需要插值的索引
            pupil_col: 瞳孔数据列名
            time_points: 需要插值的时间点

        Returns:
            Optional[np.ndarray]: 插值后的瞳孔值，如果失败返回None
        """
        try:
            # 获取眨眼段前后的数据点用于拟合
            start_idx = indices[0]
            end_idx = indices[-1]

            # 获取前面的拟合点
            before_start = max(0, start_idx - self.quadratic_fit_points)
            before_indices = list(range(before_start, start_idx))

            # 获取后面的拟合点
            after_end = min(len(samples), end_idx + 1 + self.quadratic_fit_points)
            after_indices = list(range(end_idx + 1, after_end))

            # 合并前后的索引
            fit_indices = before_indices + after_indices

            if len(fit_indices) < 3:  # 二次拟合至少需要3个点
                logger.warning(f"拟合点不足({len(fit_indices)}个)，回退到线性插值")
                return self._apply_traditional_interpolation(samples, indices, pupil_col, time_points)
            # print("用于拟合的点数", len(fit_indices))
            
            # 获取拟合用的时间和瞳孔值
            fit_times = []
            fit_values = []

            for idx in fit_indices:
                if idx < len(samples):
                    pupil_value = samples.iloc[idx][pupil_col]
                    if not pd.isna(pupil_value):
                        fit_times.append(samples.iloc[idx]['time'])
                        fit_values.append(pupil_value)

            if len(fit_times) < 3:
                logger.warning(f"有效拟合点不足({len(fit_times)}个)，回退到线性插值")
                return self._apply_traditional_interpolation(samples, indices, pupil_col, time_points)

            # 转换为numpy数组
            fit_times = np.array(fit_times)
            fit_values = np.array(fit_values)

            # 执行二次曲线拟合插值
            interpolated_values = self._quadratic_interpolation(time_points, fit_times, fit_values, poly_deg)

            logger.debug(f"二次曲线拟合成功，使用{len(fit_times)}个拟合点，插值{len(time_points)}个点")
            return interpolated_values

        except Exception as e:
            logger.warning(f"二次曲线插值失败: {e}，回退到线性插值")
            return self._apply_traditional_interpolation(samples, indices, pupil_col, time_points)

    def _apply_traditional_interpolation(self, samples: pd.DataFrame,
                                       indices: pd.Index,
                                       pupil_col: str,
                                       time_points: np.ndarray) -> Optional[np.ndarray]:
        """
        应用传统的线性插值方法

        Args:
            samples: 样本数据
            indices: 需要插值的索引
            pupil_col: 瞳孔数据列名
            time_points: 需要插值的时间点

        Returns:
            Optional[np.ndarray]: 插值后的瞳孔值，如果失败返回None
        """
        try:
            # 找到眨眼前后的有效数据点进行插值
            before_idx = indices[0] - 1
            after_idx = indices[-1] + 1

            if before_idx >= 0 and after_idx < len(samples):
                before_value = samples.iloc[before_idx][pupil_col]
                after_value = samples.iloc[after_idx][pupil_col]

                # 如果前后都有有效值，进行线性插值
                if not pd.isna(before_value) and not pd.isna(after_value):
                    start_time_interp = samples.iloc[before_idx]['time']
                    end_time_interp = samples.iloc[after_idx]['time']

                    # 线性插值
                    interpolated_values = np.interp(
                        time_points,
                        [start_time_interp, end_time_interp],
                        [before_value, after_value]
                    )

                    return interpolated_values

            return None

        except Exception as e:
            logger.warning(f"传统插值失败: {e}")
            return None

    def apply_smoothing(self, data: pd.Series) -> pd.Series:
        """
        应用滑动窗口平滑

        Args:
            data: 输入数据序列

        Returns:
            pd.Series: 平滑后的数据
        """
        if len(data) < self.smooth_window:
            logger.warning(f"数据长度 {len(data)} 小于滑动窗口 {self.smooth_window}，跳过平滑")
            return data

        # 记录当前的NaN位置（而不是原始数据的NaN位置）
        current_nan_mask = data.isna()

        if current_nan_mask.all():
            logger.warning("所有数据都是NaN，跳过平滑")
            return data

        # 如果没有NaN，直接平滑
        if not current_nan_mask.any():
            smoothed = uniform_filter1d(data.values, size=self.smooth_window, mode='nearest')
            return pd.Series(smoothed, index=data.index)

        # 如果有NaN，需要特殊处理
        # 临时插值处理NaN，但要确保插值成功
        temp_data = data.interpolate(method='linear')

        # 检查插值后是否还有NaN（可能在开头或结尾）
        if temp_data.isna().any():
            # 前向填充和后向填充处理边界NaN
            temp_data = temp_data.ffill().bfill()

        # 如果仍有NaN，使用均值填充
        if temp_data.isna().any():
            temp_data = temp_data.fillna(temp_data.mean())

        # 应用滑动平均
        smoothed = uniform_filter1d(temp_data.values, size=self.smooth_window, mode='nearest')

        # 创建结果Series
        result = pd.Series(smoothed, index=data.index)

        # 只恢复当前仍然是NaN的位置
        result[current_nan_mask] = np.nan

        return result

    def apply_pfe_correction(self, samples: pd.DataFrame) -> pd.DataFrame:
        """
        应用PFE瞳孔前缩误差矫正

        Args:
            samples: 样本数据

        Returns:
            pd.DataFrame: 矫正后的数据
        """
        try:
            # 导入PFE矫正模块
            import sys
            import os
            # 使用绝对路径添加pfe目录到sys.path
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            pfe_path = os.path.join(project_root, 'pfe')
            if pfe_path not in sys.path:
                sys.path.insert(0, pfe_path)
            
            # 直接导入pfe_correction模块
            import pfe_correction

            # 应用PFE矫正
            corrected_samples = pfe_correction.apply_pfe_correction(
                samples=samples,
                enable_correction=self.enable_pfe_correction,
                angle_threshold_degrees=self.pfe_angle_threshold_degrees
            )

            logger.info("PFE矫正应用完成")
            return corrected_samples

        except ImportError as e:
            logger.error(f"无法导入PFE矫正模块: {e}")
            logger.warning("跳过PFE矫正")
            return samples
        except Exception as e:
            logger.error(f"PFE矫正应用失败: {e}")
            logger.warning("跳过PFE矫正")
            return samples
    
    def preprocess_edf(self, edf_path: str) -> pd.DataFrame:
        """
        完整的EDF数据预处理流程

        Args:
            edf_path: EDF文件路径

        Returns:
            pd.DataFrame: 预处理后的数据
        """
        logger.info(f"开始预处理EDF文件: {edf_path}")

        # 首先尝试加载缓存数据
        cached_data = self.load_cached_data(edf_path)
        if cached_data is not None:
            logger.info("使用缓存的预处理数据")
            return cached_data

        # 缓存不存在或参数不匹配，执行完整的预处理流程
        logger.info("执行完整的数据预处理流程")

        # 1. 加载数据
        samples, events, messages = self.load_edf_data(edf_path)
        if samples is None:
            logger.error("无法加载EDF数据")
            return pd.DataFrame()
        
        # 2. 清理异常值
        logger.info("步骤1: 清理异常值")
        cleaned_samples = self.clean_pupil_data(samples)

        # 3. 速度过滤
        logger.info("步骤2: 速度过滤")
        velocity_filtered_samples = self.apply_velocity_filter(cleaned_samples)

        # 4. 有效区间长度过滤
        logger.info("步骤3: 有效区间长度过滤")
        segment_filtered_samples = self.apply_valid_segment_filter(velocity_filtered_samples)

        # 5. 提取眨眼时间段
        logger.info("步骤4: 提取眨眼时间段")
        blink_periods = self.extract_blink_periods(events)

        # 6. 眨眼插值
        logger.info("步骤5: 眨眼插值处理")
        interpolated_samples = self.apply_blink_interpolation(segment_filtered_samples, blink_periods)

        # 6.5. PFE瞳孔前缩误差矫正
        if self.enable_pfe_correction:
            logger.info("步骤6: PFE瞳孔前缩误差矫正")
            interpolated_samples = self.apply_pfe_correction(interpolated_samples)
        else:
            logger.info("步骤6: 跳过PFE矫正（已禁用）")

        
        
        # 7. 滑动窗口平滑
        logger.info("步骤7: 滑动窗口平滑")
        if 'pa_left' in interpolated_samples.columns:
            interpolated_samples['pa_left'] = self.apply_smoothing(interpolated_samples['pa_left'])
        if 'pa_right' in interpolated_samples.columns:
            interpolated_samples['pa_right'] = self.apply_smoothing(interpolated_samples['pa_right'])


        # 9. 根据眼睛选择参数计算瞳孔数据
        logger.info(f"步骤8: 根据眼睛选择参数计算瞳孔数据 (选择: {self.eye_selection})")

        if self.eye_selection == 'left':
            # 仅使用左眼数据
            if 'pa_left' in interpolated_samples.columns:
                interpolated_samples['pupil_avg'] = interpolated_samples['pa_left']
                logger.info("使用左眼数据")
            else:
                logger.warning("左眼数据不存在，无法创建pupil_avg列")

        elif self.eye_selection == 'right':
            # 仅使用右眼数据
            if 'pa_right' in interpolated_samples.columns:
                interpolated_samples['pupil_avg'] = interpolated_samples['pa_right']
                logger.info("使用右眼数据")
            else:
                logger.warning("右眼数据不存在，无法创建pupil_avg列")

        elif self.eye_selection == 'binocular':
            # 使用双眼平均（原有逻辑）
            if 'pa_left' in interpolated_samples.columns and 'pa_right' in interpolated_samples.columns:
                interpolated_samples['pupil_avg'] = interpolated_samples[['pa_left', 'pa_right']].mean(axis=1)
                logger.info("使用双眼平均数据")
            elif 'pa_left' in interpolated_samples.columns:
                interpolated_samples['pupil_avg'] = interpolated_samples['pa_left']
                logger.info("仅左眼数据可用，使用左眼数据")
            elif 'pa_right' in interpolated_samples.columns:
                interpolated_samples['pupil_avg'] = interpolated_samples['pa_right']
                logger.info("仅右眼数据可用，使用右眼数据")
            else:
                logger.warning("左眼和右眼数据都不存在，无法创建pupil_avg列")

        # 保存缓存数据
        self.save_cached_data(edf_path, interpolated_samples)

        logger.info(f"预处理完成，输出数据包含 {len(interpolated_samples)} 个样本")

        return interpolated_samples

def preprocess_edf_file(edf_path: str,
                       smooth_window: int = 5,
                       blink_interpolation_window: int = 40,
                       interpolation_method: str = 'quadratic',
                       velocity_threshold: float = 500.0,
                       quadratic_fit_points: int = 100,
                       min_valid_segment_length: int = 50,
                       eye_selection: str = 'binocular',
                       enable_pfe_correction: bool = False,
                       pfe_angle_threshold_degrees: float = 30.0) -> pd.DataFrame:
    """
    便捷函数：预处理EDF文件

    Args:
        edf_path: EDF文件路径
        smooth_window: 滑动窗口大小
        blink_interpolation_window: 眨眼插值窗口大小（毫秒）
        interpolation_method: 插值方法 ('linear', 'cubic', 'nearest', 'quadratic')
        velocity_threshold: 速度阈值（像素/毫秒）
        quadratic_fit_points: 二次曲线拟合时使用的前后数据点数
        min_valid_segment_length: 最小有效区间长度（数据点数，0代表关闭）
        eye_selection: 眼睛选择 ('left'=仅左眼, 'right'=仅右眼, 'binocular'=双眼平均)
        enable_pfe_correction: 是否启用PFE瞳孔前缩误差矫正
        pfe_angle_threshold_degrees: PFE角度阈值（度），只有在此角度内才进行PFE补偿

    Returns:
        pd.DataFrame: 预处理后的数据
    """
    preprocessor = EyeDataPreprocessor(
        smooth_window=smooth_window,
        blink_interpolation_window=blink_interpolation_window,
        interpolation_method=interpolation_method,
        velocity_threshold=velocity_threshold,
        quadratic_fit_points=quadratic_fit_points,
        min_valid_segment_length=min_valid_segment_length,
        eye_selection=eye_selection,
        enable_pfe_correction=enable_pfe_correction,
        pfe_angle_threshold_degrees=pfe_angle_threshold_degrees
    )

    return preprocessor.preprocess_edf(edf_path)

def extract_trial_segment(edf_path: str,
                         trial_num: int,
                         start_event: str = 'baseline_start',
                         end_event: str = 'question_end',
                         smooth_window: int = 5,
                         blink_interpolation_window: int = 0) -> pd.DataFrame:
    """
    便捷函数：提取指定试次的特定时间段数据

    Args:
        edf_path: EDF文件路径
        trial_num: 试次号
        start_event: 开始事件 ('trial_start', 'baseline_start', 'question_start')
        end_event: 结束事件 ('question_end', 'trial_end')
        smooth_window: 滑动窗口大小
        blink_interpolation_window: 眨眼插值窗口大小（毫秒）

    Returns:
        pd.DataFrame: 提取的试次数据段
    """
    # 1. 预处理数据
    preprocessor = EyeDataPreprocessor(
        smooth_window=smooth_window,
        blink_interpolation_window=blink_interpolation_window
    )

    # 2. 加载数据
    samples, events, messages = preprocessor.load_edf_data(edf_path)
    if samples is None:
        logger.error("无法加载EDF数据")
        return pd.DataFrame()

    # 3. 预处理
    processed_samples = preprocessor.preprocess_edf(edf_path)

    # 4. 提取试次消息
    from test_preprocessing import extract_trial_messages, extract_trial_data
    trial_messages = extract_trial_messages(messages)

    # 5. 提取指定试次数据
    trial_data = extract_trial_data(processed_samples, trial_messages, trial_num)

    return trial_data

def plot_pupil_comparison(original_data: pd.DataFrame,
                         processed_data: pd.DataFrame,
                         save_path: Optional[str] = None,
                         time_range: Optional[Tuple[float, float]] = None,
                         y_range: Optional[Tuple[float, float]] = None,
                         show_plot: bool = True) -> None:
    """
    绘制数据处理前后的瞳孔数据对比图

    Args:
        original_data: 原始数据
        processed_data: 处理后的数据
        save_path: 保存路径
        time_range: 显示的时间范围 (start_time, end_time)，单位毫秒
        y_range: y轴显示范围 (min_y, max_y)，例如 (3500, 5500)
        show_plot: 是否显示图形
    """
    logger.info("开始绘制瞳孔数据对比图...")

    # 重新设置字体
    setup_chinese_font()

    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Pupil Data Preprocessing Comparison', fontsize=16, fontweight='bold')

    # 检查数据是否为空
    if original_data.empty or processed_data.empty:
        logger.error("输入数据为空，无法绘制对比图")
        return

    logger.info(f"原始数据形状: {original_data.shape}")
    logger.info(f"处理后数据形状: {processed_data.shape}")

    # 如果指定了时间范围，则过滤数据
    if time_range:
        start_time, end_time = time_range
        logger.info(f"应用时间范围过滤: {start_time} - {end_time} ms")
        original_mask = (original_data['time'] >= start_time) & (original_data['time'] <= end_time)
        processed_mask = (processed_data['time'] >= start_time) & (processed_data['time'] <= end_time)
        original_subset = original_data[original_mask].copy()
        processed_subset = processed_data[processed_mask].copy()
    else:
        # 如果没有指定时间范围，使用前10000个数据点以避免图表过于密集
        max_points = 10000
        original_subset = original_data.iloc[:max_points].copy()
        processed_subset = processed_data.iloc[:max_points].copy()
        logger.info(f"使用前{max_points}个数据点进行绘图")

    logger.info(f"过滤后原始数据形状: {original_subset.shape}")
    logger.info(f"过滤后处理数据形状: {processed_subset.shape}")

    # 转换时间为秒
    original_subset['time_sec'] = original_subset['time'] / 1000
    processed_subset['time_sec'] = processed_subset['time'] / 1000

    # 绘制左眼数据对比
    if 'pa_left' in original_subset.columns and 'pa_left' in processed_subset.columns:
        logger.info("绘制左眼数据对比...")

        # 过滤有效数据
        orig_left_valid = original_subset['pa_left'].dropna()
        proc_left_valid = processed_subset['pa_left'].dropna()

        if len(orig_left_valid) > 0 and len(proc_left_valid) > 0:
            # 原始左眼数据
            orig_time = original_subset.loc[orig_left_valid.index, 'time_sec']
            proc_time = processed_subset.loc[proc_left_valid.index, 'time_sec']

            axes[0, 0].plot(orig_time, orig_left_valid,
                           'b-', alpha=0.6, linewidth=0.8, label='Original Data', markersize=1)
            axes[0, 0].plot(proc_time, proc_left_valid,
                           'r-', alpha=0.8, linewidth=1.2, label='Processed Data', markersize=1)
            axes[0, 0].set_title('Left Eye Pupil Diameter Comparison', fontsize=14)
            axes[0, 0].set_xlabel('Time (seconds)')
            axes[0, 0].set_ylabel('Pupil Diameter (pixels)')
            if y_range:
                axes[0, 0].set_ylim(y_range[0], y_range[1])
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            logger.info(f"左眼数据点数 - 原始: {len(orig_left_valid)}, 处理后: {len(proc_left_valid)}")
        else:
            axes[0, 0].text(0.5, 0.5, 'No valid left eye data',
                           transform=axes[0, 0].transAxes, ha='center', va='center')
            logger.warning("左眼没有有效数据")
    else:
        axes[0, 0].text(0.5, 0.5, 'Left eye data not available',
                       transform=axes[0, 0].transAxes, ha='center', va='center')
        logger.warning("左眼数据列不存在")

    # 绘制右眼数据对比
    if 'pa_right' in original_subset.columns and 'pa_right' in processed_subset.columns:
        logger.info("绘制右眼数据对比...")

        # 过滤有效数据
        orig_right_valid = original_subset['pa_right'].dropna()
        proc_right_valid = processed_subset['pa_right'].dropna()

        if len(orig_right_valid) > 0 and len(proc_right_valid) > 0:
            # 原始右眼数据
            orig_time = original_subset.loc[orig_right_valid.index, 'time_sec']
            proc_time = processed_subset.loc[proc_right_valid.index, 'time_sec']

            axes[0, 1].plot(orig_time, orig_right_valid,
                           'b-', alpha=0.6, linewidth=0.8, label='Original Data', markersize=1)
            axes[0, 1].plot(proc_time, proc_right_valid,
                           'r-', alpha=0.8, linewidth=1.2, label='Processed Data', markersize=1)
            axes[0, 1].set_title('Right Eye Pupil Diameter Comparison', fontsize=14)
            axes[0, 1].set_xlabel('Time (seconds)')
            axes[0, 1].set_ylabel('Pupil Diameter (pixels)')
            if y_range:
                axes[0, 1].set_ylim(y_range[0], y_range[1])
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            logger.info(f"右眼数据点数 - 原始: {len(orig_right_valid)}, 处理后: {len(proc_right_valid)}")
        else:
            axes[0, 1].text(0.5, 0.5, 'No valid right eye data',
                           transform=axes[0, 1].transAxes, ha='center', va='center')
            logger.warning("右眼没有有效数据")
    else:
        axes[0, 1].text(0.5, 0.5, 'Right eye data not available',
                       transform=axes[0, 1].transAxes, ha='center', va='center')
        logger.warning("右眼数据列不存在")

    # 绘制双眼平均对比
    logger.info("绘制双眼平均数据对比...")

    # 计算原始数据的双眼平均
    if 'pa_left' in original_subset.columns and 'pa_right' in original_subset.columns:
        original_subset['pupil_avg'] = original_subset[['pa_left', 'pa_right']].mean(axis=1)
    elif 'pa_left' in original_subset.columns:
        original_subset['pupil_avg'] = original_subset['pa_left']
    elif 'pa_right' in original_subset.columns:
        original_subset['pupil_avg'] = original_subset['pa_right']

    if 'pupil_avg' in original_subset.columns and 'pupil_avg' in processed_subset.columns:
        # 过滤有效数据
        orig_avg_valid = original_subset['pupil_avg'].dropna()
        proc_avg_valid = processed_subset['pupil_avg'].dropna()

        if len(orig_avg_valid) > 0 and len(proc_avg_valid) > 0:
            orig_time = original_subset.loc[orig_avg_valid.index, 'time_sec']
            proc_time = processed_subset.loc[proc_avg_valid.index, 'time_sec']

            axes[1, 0].plot(orig_time, orig_avg_valid,
                           'b-', alpha=0.6, linewidth=0.8, label='Original Data', markersize=1)
            axes[1, 0].plot(proc_time, proc_avg_valid,
                           'r-', alpha=0.8, linewidth=1.2, label='Processed Data', markersize=1)
            axes[1, 0].set_title('Binocular Average Pupil Diameter Comparison', fontsize=14)
            axes[1, 0].set_xlabel('Time (seconds)')
            axes[1, 0].set_ylabel('Pupil Diameter (pixels)')
            if y_range:
                axes[1, 0].set_ylim(y_range[0], y_range[1])
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            logger.info(f"双眼平均数据点数 - 原始: {len(orig_avg_valid)}, 处理后: {len(proc_avg_valid)}")
        else:
            axes[1, 0].text(0.5, 0.5, 'No valid binocular average data',
                           transform=axes[1, 0].transAxes, ha='center', va='center')
            logger.warning("双眼平均没有有效数据")
    else:
        axes[1, 0].text(0.5, 0.5, 'Binocular average data not available',
                       transform=axes[1, 0].transAxes, ha='center', va='center')
        logger.warning("双眼平均数据列不存在")

    # 绘制数据质量统计
    logger.info("生成数据质量统计...")
    axes[1, 1].axis('off')

    # 计算数据质量统计
    stats_text = "Data Quality Statistics:\n\n"

    # 原始数据统计
    if 'pa_left' in original_subset.columns:
        left_valid_orig = (~original_subset['pa_left'].isna()).sum()
        left_total = len(original_subset)
        left_ratio_orig = left_valid_orig / left_total if left_total > 0 else 0
        stats_text += f"Left Eye Valid Data Ratio:\n  Original: {left_ratio_orig:.2%}\n"

        if 'pa_left' in processed_subset.columns:
            left_valid_proc = (~processed_subset['pa_left'].isna()).sum()
            left_ratio_proc = left_valid_proc / len(processed_subset) if len(processed_subset) > 0 else 0
            stats_text += f"  Processed: {left_ratio_proc:.2%}\n\n"

    if 'pa_right' in original_subset.columns:
        right_valid_orig = (~original_subset['pa_right'].isna()).sum()
        right_total = len(original_subset)
        right_ratio_orig = right_valid_orig / right_total if right_total > 0 else 0
        stats_text += f"Right Eye Valid Data Ratio:\n  Original: {right_ratio_orig:.2%}\n"

        if 'pa_right' in processed_subset.columns:
            right_valid_proc = (~processed_subset['pa_right'].isna()).sum()
            right_ratio_proc = right_valid_proc / len(processed_subset) if len(processed_subset) > 0 else 0
            stats_text += f"  Processed: {right_ratio_proc:.2%}\n\n"

    # 双眼平均统计
    if 'pupil_avg' in original_subset.columns and 'pupil_avg' in processed_subset.columns:
        avg_valid_orig = (~original_subset['pupil_avg'].isna()).sum()
        avg_ratio_orig = avg_valid_orig / len(original_subset) if len(original_subset) > 0 else 0
        avg_valid_proc = (~processed_subset['pupil_avg'].isna()).sum()
        avg_ratio_proc = avg_valid_proc / len(processed_subset) if len(processed_subset) > 0 else 0

        stats_text += f"Binocular Avg Valid Data Ratio:\n  Original: {avg_ratio_orig:.2%}\n  Processed: {avg_ratio_proc:.2%}\n\n"

        # 均值和标准差
        orig_mean = original_subset['pupil_avg'].mean()
        orig_std = original_subset['pupil_avg'].std()
        proc_mean = processed_subset['pupil_avg'].mean()
        proc_std = processed_subset['pupil_avg'].std()

        if not pd.isna(orig_mean) and not pd.isna(proc_mean):
            stats_text += f"Binocular Avg Pupil Diameter:\n"
            stats_text += f"  Original: {orig_mean:.1f}±{orig_std:.1f}\n"
            stats_text += f"  Processed: {proc_mean:.1f}±{proc_std:.1f}"

    axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                    fontsize=10, verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

    plt.tight_layout()

    # 保存图形
    if save_path:
        try:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            logger.info(f"对比图已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存图形失败: {e}")

    # 显示图形
    if show_plot:
        try:
            plt.show()
            logger.info("图形显示完成")
        except Exception as e:
            logger.error(f"显示图形失败: {e}")
    else:
        plt.close()
        logger.info("图形已关闭")

if __name__ == "__main__":
    # 测试代码
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250716_143501_zhaojingyi/zhaojingyi.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_151616_dulu/dulu.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_160729_dulu/dulu.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250725_162711_sunyan_continuous_reading/sunyan.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250718_163043_dulu_continuous/dulu_continuous.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250729_161339_test_161339_continuous_reading/test_161339.edf"
    # edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250801_154631_mzx_continuous_reading/mzx.edf"
    edf_path = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250803_154304_9pt_test/test_154258.edf"

    
    
    # 创建预处理器 - 测试二次曲线插值
    preprocessor = EyeDataPreprocessor(
        smooth_window=150,  #之后可以加入汉宁窗50~150ms滤波，或者是4hz的截止频率获取以下的滤波值
        blink_interpolation_window=40,  # 无用参数
        interpolation_method='cubic',  # 使用二次曲线插值 cubic quadratic
        min_pupil_size=500.0,
        max_pupil_size=6000.0,
        sampling_rate=1000,
        velocity_threshold=10.0,
        quadratic_fit_points=300,  # 使用前后x个点进行拟合
        min_valid_segment_length=200,  # 最小有效区间长度，小于它直接扔掉
        eye_selection='left',  # 眼睛选择: 'left', 'right', 'binocular'
        enable_pfe_correction=True,  # 是否启用PFE矫正
        pfe_angle_threshold_degrees=60.0,  # PFE角度阈值（度）
    )

    # 1. 加载原始数据
    logger.info("加载原始数据...")
    original_samples, events, messages = preprocessor.load_edf_data(edf_path)
    # print(original_samples.head())
    # print(original_samples['gx_left'])
    # print(original_samples['gy_left'])

    if original_samples is None:
        logger.error("无法加载EDF数据")
        exit(1)

    # 2. 执行预处理
    logger.info("执行数据预处理...")
    processed_data = preprocessor.preprocess_edf(edf_path)

    if not processed_data.empty and original_samples is not None:
        print(f"\n预处理结果:")
        print(f"原始数据形状: {original_samples.shape}")
        print(f"处理后数据形状: {processed_data.shape}")
        print(f"列名: {list(processed_data.columns)}")
        print(f"时间范围: {processed_data['time'].min()} - {processed_data['time'].max()}")

        if 'pupil_avg' in processed_data.columns:
            print(f"双眼平均瞳孔直径统计:")
            print(f"  均值: {processed_data['pupil_avg'].mean():.2f}")
            print(f"  标准差: {processed_data['pupil_avg'].std():.2f}")
            print(f"  有效数据比例: {(~processed_data['pupil_avg'].isna()).mean():.2%}")

        # 3. 绘制数据处理前后对比图
        logger.info("绘制数据处理前后对比图...")

        # 创建保存目录
        figures_dir = "./figures"
        os.makedirs(figures_dir, exist_ok=True)

        # 设置保存路径
        save_path = os.path.join(figures_dir, "pupil_preprocessing_comparison.png")

        # 检查数据的时间范围
        min_time = processed_data['time'].min()
        max_time = processed_data['time'].max()
        logger.info(f"数据时间范围: {min_time} - {max_time} ms")

        # 设置时间范围（显示前60秒的数据，从数据开始时间算起）
        time_range = (min_time+000, max_time+000)  # 从开始时间起60秒，单位毫秒
        logger.info(f"绘图时间范围: {time_range[0]} - {time_range[1]} ms")

        # 绘制对比图
        plot_pupil_comparison(
            original_data=original_samples,
            processed_data=processed_data,
            save_path=save_path,
            time_range=time_range,  # 可以设为None显示全部数据
            y_range=None,  # 指定y轴范围，可以设为None使用自动范围
            # time_range=None,
            show_plot=True
        )

        print(f"\n对比图已保存到: {save_path}")

    else:
        logger.error("预处理失败或数据为空")
